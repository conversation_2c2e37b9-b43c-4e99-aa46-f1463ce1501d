import numpy as np
import qutip as qt
from scipy.optimize import minimize
from matplotlib.colors import TwoSlopeNorm
import matplotlib.pyplot as plt
# Minimal simulator for the interferometer, post-selection, and optimization
# Parameters x = (alpha, phi_bs, theta, |xi|, phi_xi, |beta|, phi_beta)


def create_target_state(r: float, xi_db: float, N: int) -> qt.Qobj:
    """Target cubic phase state |psi_T> = exp(i r q^3) S(xi_T) |0>.
    xi_T = -log(10^{xi_db/20}). Use q = (a + a^\dag)/sqrt(2).
    """
    ten_to_power = 10 ** (xi_db / 20.0)
    xi_T = -np.log(ten_to_power)
    r_sq = abs(xi_T)
    # Use negative sign to squeeze p (broad q, narrow p), following the given reference snippet
    S = qt.squeeze(N, -r_sq)
    vac = qt.basis(N, 0)
    squeezed = S * vac
    a = qt.destroy(N)
    adag = qt.create(N)
    q = (a + adag) / np.sqrt(2.0)
    cubic = (1j * r * (q ** 3)).expm()
    return cubic * squeezed


def _ops_two_mode(N: int):
    a1 = qt.destroy(N)
    a2 = qt.destroy(N)
    I = qt.qeye(N)
    return a1, a2, I


def unitary_total(x, N: int) -> qt.Qobj:
    """Build U_total(x) = D2(beta) S(xi) R2(theta) B(phi_bs) D2(alpha)."""
    alpha, phi_bs, theta, xi_abs, phi_xi, beta_abs, phi_beta = x
    xi = xi_abs * np.exp(1j * phi_xi)
    beta = beta_abs * np.exp(1j * phi_beta)

    a1, a2, I = _ops_two_mode(N)

    # D2(alpha) and D2(beta): displacements on mode 2 only
    D2_alpha = qt.tensor(I, qt.displace(N, alpha))
    D2_beta = qt.tensor(I, qt.displace(N, beta))

    # Beam splitter between modes 1 and 2
    H_bs = qt.tensor(a1.dag(), a2) + qt.tensor(a1, a2.dag())
    B = (1j * phi_bs * H_bs).expm()

    # Phase rotation on mode 2
    n2 = a2.dag() * a2
    R2 = qt.tensor(I, (1j * theta * n2).expm())

    # Two-mode squeezing with complex parameter xi
    S = (xi.conjugate() * qt.tensor(a1, a2) - xi * qt.tensor(a1.dag(), a2.dag())).expm()

    return D2_beta * S * R2 * B * D2_alpha


def simulate_and_metrics(x, target_state: qt.Qobj, N: int):
    """Simulate evolution, post-select on |2> in mode 1.

    Returns:
        prob (float): Post-selection success probability.
        fidelity (float): <psi_T| rho2_norm |psi_T> with target_state (pure).
        loss (float): 1 - fidelity.
        rho2_norm (qt.Qobj or None): Normalized conditional density matrix of mode 2; None if prob == 0.
    """
    a1, a2, I = _ops_two_mode(N)
    psi0 = qt.tensor(qt.fock(N, 2), qt.fock(N, 0))

    U = unitary_total(x, N)
    psi_evolved = U * psi0

    # Projector on |2><2| on mode 1
    Pi = qt.tensor(qt.fock(N, 2) * qt.fock(N, 2).dag(), I)

    # Post-selected (unnormalized) density matrix and success probability
    rho = psi_evolved * psi_evolved.dag()
    rho_post = Pi * rho * Pi
    prob = float(np.real(rho_post.tr()))
    print(prob)
    if prob <= 0:
        return 0.0, 0.0, 1.0, None

    # Reduced state of mode 2 (normalized)
    rho2 = qt.ptrace(rho_post, 1)
    rho2_norm = (rho2 / prob).unit() if rho2.tr() != 0 else rho2

    # Fidelity with pure target_state: F = <phi| rho |phi>
    fidelity = float(np.real(qt.expect(qt.ket2dm(target_state), rho2_norm)))
    fidelity = max(0.0, min(1.0, fidelity))
    loss = 1.0 - fidelity
    return prob, fidelity, loss, rho2_norm


def objective_loss(x, target_state: qt.Qobj, N: int):
    # SciPy will estimate gradients (finite differences) when jac=False.
    _, _, loss, _ = simulate_and_metrics(x, target_state, N)
    return loss


def optimize_params(target_state: qt.Qobj, N: int, x0=None, bounds=None, maxiter: int = 50, phi_bs_fixed=None):
    """Run L-BFGS-B to minimize loss. If phi_bs_fixed is not None, keep it fixed.
    x layout: (alpha, phi_bs, theta, |xi|, phi_xi, |beta|, phi_beta)
    """
    if x0 is None:
        # Reasonable random init
        rng = np.random.default_rng(0)
        x0 = np.array([
            rng.normal(scale=0.2),            # alpha (real)
            rng.uniform(-np.pi/2, np.pi/2),   # phi_bs
            rng.uniform(-np.pi, np.pi),       # theta
            abs(rng.normal(scale=0.2)),       # |xi|
            rng.uniform(-np.pi, np.pi),       # phi_xi
            abs(rng.normal(scale=0.2)),       # |beta|
            rng.uniform(-np.pi, np.pi),       # phi_beta
        ], dtype=float)

    x0 = np.array(x0, dtype=float)

    if phi_bs_fixed is not None:
        # Reparameterize: optimize over 6 vars, inject fixed phi_bs
        mask = np.array([True, False, True, True, True, True, True])
        x0_red = x0[mask]

        def _pack(x_red):
            x_full = np.zeros(7, dtype=float)
            x_full[mask] = x_red
            x_full[1] = phi_bs_fixed
            return x_full

        def fun_red(x_red):
            return objective_loss(_pack(x_red), target_state, N)

        res = minimize(fun_red, x0_red, method='L-BFGS-B', bounds=None, options={"maxiter": maxiter})
        x_opt = _pack(res.x)
    prob, fid, loss, _ = simulate_and_metrics(x_opt, target_state, N)
    return x_opt, {"loss": loss, "fidelity": fid, "prob": prob, "nit": res.nit, "success": res.success}

    # Unconstrained over 7 vars
    res = minimize(lambda x: objective_loss(x, target_state, N), x0, method='L-BFGS-B', bounds=bounds, options={"maxiter": maxiter})
    x_opt = res.x
    prob, fid, loss, _ = simulate_and_metrics(x_opt, target_state, N)
    return x_opt, {"loss": loss, "fidelity": fid, "prob": prob, "nit": res.nit, "success": res.success}


if __name__ == "__main__":
    # Example: reproduce a single target state's optimization (Strategy 1 minimal demo)
    N = 50
    r = 0.15
    xi_db = 5.0
    target = create_target_state(r, xi_db, N)
    xvec = np.linspace(-5, 5, 200)
    W = qt.wigner(target, xvec, xvec,g=np.sqrt(2))

# Plot
    plt.figure(figsize=(6, 5))
    norm = TwoSlopeNorm(vmin=W.min(), vcenter=0, vmax=W.max())
    plt.contourf(xvec, xvec, W, 200, cmap='RdBu_r',norm=norm)
    plt.colorbar()
    plt.xlabel('q')
    plt.ylabel('p')
    plt.title('Target State Wigner Function\nr=0.15, ξ=5.0 dB')
    plt.show()
    x=[0.0005, 0.49993*np.pi, 0.50034*np.pi, 0.0003, 0.50023*np.pi, 1.5725, 1.50020*np.pi]
    x=[]
    prob, fid, loss, rho2_norm = simulate_and_metrics(x, target, N)
    if rho2_norm is None:
        print("Post-selection probability is zero; cannot plot conditional Wigner function.")
        # Try a quick random search to find a set with non-zero probability for demonstration
        rng = np.random.default_rng(42)
        found = False
        for _ in range(100):
            xr = [
                rng.normal(scale=0.3),                 # alpha
                rng.uniform(-np.pi/2, np.pi/2),         # phi_bs
                rng.uniform(-np.pi, np.pi),             # theta
                abs(rng.normal(scale=0.3)),             # |xi|
                rng.uniform(-np.pi, np.pi),             # phi_xi
                abs(rng.normal(scale=0.5)),             # |beta|
                rng.uniform(-np.pi, np.pi),             # phi_beta
            ]
            prob_r, fid_r, loss_r, rho2_r = simulate_and_metrics(xr, target, N)
            if rho2_r is not None and prob_r > 1e-10:
                print(f"Found demo params with prob={prob_r:.3e}, fid={fid_r:.4f}, loss={loss_r:.4f}")
                W_r = qt.wigner(rho2_r, xvec, xvec, g=np.sqrt(2))
                plt.figure(figsize=(6, 5))
                norm_r = TwoSlopeNorm(vmin=W_r.min(), vcenter=0, vmax=W_r.max())
                plt.contourf(xvec, xvec, W_r, 200, cmap='RdBu_r', norm=norm_r)
                plt.colorbar()
                plt.xlabel('q')
                plt.ylabel('p')
                plt.title('Random Demo Conditional Wigner')
                plt.show()
                found = True
                break
        if not found:
            print("Random search (100 tries) didn't find a non-zero probability example.")
    else:
        print(f"Post-selection prob={prob:.4e}, fidelity={fid:.4f}, loss={loss:.4f}")
        W = qt.wigner(rho2_norm, xvec, xvec, g=np.sqrt(2))
        plt.figure(figsize=(6, 5))
        norm = TwoSlopeNorm(vmin=W.min(), vcenter=0, vmax=W.max())
        plt.contourf(xvec, xvec, W, 200, cmap='RdBu_r', norm=norm)
        plt.colorbar()
        plt.xlabel('q')
        plt.ylabel('p')
        plt.title('Conditional Output Wigner Function')
        plt.show()

    # x_opt, info = optimize_params(target, N, maxiter=30)
    # print("Optimized params:", x_opt)
    # print("Metrics:", info)

