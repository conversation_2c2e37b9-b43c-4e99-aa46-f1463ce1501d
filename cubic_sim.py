import numpy as np
import qutip as qt
from scipy.optimize import minimize
from matplotlib.colors import TwoSlopeNorm
import matplotlib.pyplot as plt
# Minimal simulator for the interferometer, post-selection, and optimization
# Parameters x = (alpha, phi_bs, theta, |xi|, phi_xi, |beta|, phi_beta)


def create_target_state(r: float, xi_db: float, N: int) -> qt.Qobj:
        """Construct target cubic phase state |psi_T> = exp(i r q^3) S(xi_T)|0>.

        Conventions (aligned with standard CV and paper Eq.(2)):
            q = (a + a^\dag)/sqrt(2)
            xi_T = - ln(10^{xi_db/20})  (real, negative for positive dB)

        Note: Using sqrt(2) (NOT 2) in quadrature definition. Earlier version used 1/2
        which rescales effective cubicity and breaks fidelity comparisons.
        """
        xi_T = -np.log(10 ** (xi_db / 20.0))  # real
        S = qt.squeeze(N, xi_T)               # real squeezing parameter
        vac = qt.basis(N, 0)
        squeezed = S * vac
        a = qt.destroy(N)
        q = (a + a.dag()) / np.sqrt(2.0)
        cubic = (1j * r * (q ** 3)).expm()
        return cubic * squeezed


def _ops_two_mode(N: int):
    a1 = qt.destroy(N)
    a2 = qt.destroy(N)
    I = qt.qeye(N)
    return a1, a2, I


def unitary_total(x, N: int) -> qt.Qobj:
    """Build U_total(x) = D2(beta) S(xi) R2(theta) B(phi_bs) D2(alpha)."""
    alpha, phi_bs, theta, xi_abs, phi_xi, beta_abs, phi_beta = x
    xi = xi_abs * np.exp(1j * phi_xi)
    beta = beta_abs * np.exp(1j * phi_beta)

    a1, a2, I = _ops_two_mode(N)

    # D2(alpha) and D2(beta): displacements on mode 2 only
    D2_alpha = qt.tensor(I, qt.displace(N, alpha))
    D2_beta = qt.tensor(I, qt.displace(N, beta))

    # Beam splitter between modes 1 and 2
    H_bs = qt.tensor(a1.dag(), a2) + qt.tensor(a1, a2.dag())
    B = (1j * phi_bs * H_bs).expm()

    # Phase rotation on mode 2
    n2 = a2.dag() * a2
    R2 = qt.tensor(I, (1j * theta * n2).expm())

    # Two-mode squeezing with complex parameter xi
    S = (xi.conjugate() * qt.tensor(a1, a2) - xi * qt.tensor(a1.dag(), a2.dag())).expm()

    return D2_beta * S * R2 * B * D2_alpha


def unitary_total_variant(x, N: int, *, bs_form: str = 'plus', tms_sign: str = 'std') -> qt.Qobj:
    """Variant builder to test convention differences.
    bs_form: 'plus' uses exp(i φ (a1^† a2 + a1 a2^†)); 'minus' uses exp(φ (a1 a2^† - a1^† a2)).
    tms_sign: 'std' uses exp( ξ* a1 a2 - ξ a1^† a2^† ); 'alt' uses exp( ξ a1^† a2^† - ξ* a1 a2 ).
    """
    alpha, phi_bs, theta, xi_abs, phi_xi, beta_abs, phi_beta = x
    xi = xi_abs * np.exp(1j * phi_xi)
    beta = beta_abs * np.exp(1j * phi_beta)
    a1, a2, I = _ops_two_mode(N)
    D2_alpha = qt.tensor(I, qt.displace(N, alpha))
    D2_beta = qt.tensor(I, qt.displace(N, beta))
    if bs_form == 'plus':
        H_bs = qt.tensor(a1.dag(), a2) + qt.tensor(a1, a2.dag())
        B = (1j * phi_bs * H_bs).expm()
    else:  # 'minus'
        K = qt.tensor(a1, a2.dag()) - qt.tensor(a1.dag(), a2)
        B = (phi_bs * K).expm()
    n2 = a2.dag() * a2
    R2 = qt.tensor(I, (1j * theta * n2).expm())
    if tms_sign == 'std':
        S = (xi.conjugate() * qt.tensor(a1, a2) - xi * qt.tensor(a1.dag(), a2.dag())).expm()
    else:  # 'alt'
        S = (xi * qt.tensor(a1.dag(), a2.dag()) - xi.conjugate() * qt.tensor(a1, a2)).expm()
    return D2_beta * S * R2 * B * D2_alpha


def unitary_scaled(x, N: int, *, bs_scale=1.0, tms_scale=1.0, cubic_scale=1.0):
    """Return (U, target_state) using optional scaling factors:
    bs_scale: multiplies generator of beam splitter.
    tms_scale: multiplies generator of two-mode squeezing.
    cubic_scale: rescales exponent to exp(i * r * q^3 / cubic_scale).
    Used ONLY for diagnostics to match paper conventions.
    """
    alpha, phi_bs, theta, xi_abs, phi_xi, beta_abs, phi_beta = x
    xi = xi_abs * np.exp(1j * phi_xi)
    beta = beta_abs * np.exp(1j * phi_beta)
    a1, a2, I = _ops_two_mode(N)
    # Build operators
    D2_alpha = qt.tensor(I, qt.displace(N, alpha))
    D2_beta = qt.tensor(I, qt.displace(N, beta))
    H_bs = qt.tensor(a1.dag(), a2) + qt.tensor(a1, a2.dag())
    B = (1j * phi_bs * bs_scale * H_bs).expm()
    n2 = a2.dag() * a2
    R2 = qt.tensor(I, (1j * theta * n2).expm())
    G_tms = (xi.conjugate() * qt.tensor(a1, a2) - xi * qt.tensor(a1.dag(), a2.dag()))
    S = (tms_scale * G_tms).expm()
    U = D2_beta * S * R2 * B * D2_alpha
    return U


def create_target_state_scaled(r: float, xi_db: float, N: int, cubic_scale=1.0) -> qt.Qobj:
    xi_T = -np.log(10 ** (xi_db / 20.0))
    S = qt.squeeze(N, xi_T)
    vac = qt.basis(N, 0)
    squeezed = S * vac
    a = qt.destroy(N)
    q = (a + a.dag()) / np.sqrt(2.0)
    cubic = (1j * r * (q ** 3) / cubic_scale).expm()
    return cubic * squeezed


def simulate_and_metrics(x, target_state: qt.Qobj, N: int):
    """Simulate evolution, post-select on |2> in mode 1.

    Returns:
        prob (float): Post-selection success probability.
        fidelity (float): <psi_T| rho2_norm |psi_T> with target_state (pure).
        loss (float): 1 - fidelity.
        rho2_norm (qt.Qobj or None): Normalized conditional density matrix of mode 2; None if prob == 0.
    """
    a1, a2, I = _ops_two_mode(N)
    psi0 = qt.tensor(qt.fock(N, 2), qt.fock(N, 0))

    U = unitary_total(x, N)
    psi_evolved = U * psi0

    # Projector on |2><2| on mode 1
    Pi = qt.tensor(qt.fock(N, 2) * qt.fock(N, 2).dag(), I)

    # Post-selected (unnormalized) density matrix and success probability
    rho = psi_evolved * psi_evolved.dag()
    rho_post = Pi * rho * Pi
    prob = float(np.real(rho_post.tr()))
    if prob <= 0:
        return 0.0, 0.0, 1.0, None

    # Reduced state of mode 2 (normalized)
    rho2 = qt.ptrace(rho_post, 1)
    rho2_norm = (rho2 / prob).unit() if rho2.tr() != 0 else rho2

    # Fidelity with pure target_state: F = <phi| rho |phi>
    fidelity = float(np.real(qt.expect(qt.ket2dm(target_state), rho2_norm)))
    fidelity = max(0.0, min(1.0, fidelity))
    loss = 1.0 - fidelity
    return prob, fidelity, loss, rho2_norm


def objective_loss(x, target_state: qt.Qobj, N: int):
    # SciPy will estimate gradients (finite differences) when jac=False.
    _, _, loss, _ = simulate_and_metrics(x, target_state, N)
    return loss


def optimize_params(target_state: qt.Qobj, N: int, x0=None, bounds=None, maxiter: int = 50, phi_bs_fixed=None):
    """(Unused for reproduction) Gradient-free wrapper using L-BFGS-B.
    x = (alpha, phi_bs, theta, |xi|, phi_xi, |beta|, phi_beta)
    """
    if x0 is None:
        rng = np.random.default_rng(0)
        x0 = np.array([
            rng.normal(scale=0.2),
            rng.uniform(-np.pi/2, np.pi/2),
            rng.uniform(-np.pi, np.pi),
            abs(rng.normal(scale=0.2)),
            rng.uniform(-np.pi, np.pi),
            abs(rng.normal(scale=0.2)),
            rng.uniform(-np.pi, np.pi),
        ], dtype=float)
    x0 = np.array(x0, dtype=float)

    if phi_bs_fixed is not None:
        mask = np.array([True, False, True, True, True, True, True])
        x0_red = x0[mask]

        def _pack(x_red):
            x_full = np.zeros(7, dtype=float)
            x_full[mask] = x_red
            x_full[1] = phi_bs_fixed
            return x_full

        def fun_red(x_red):
            return objective_loss(_pack(x_red), target_state, N)

        res = minimize(fun_red, x0_red, method='L-BFGS-B', bounds=None, options={"maxiter": maxiter})
        x_opt = _pack(res.x)
    else:
        res = minimize(lambda x: objective_loss(x, target_state, N), x0, method='L-BFGS-B', bounds=bounds, options={"maxiter": maxiter})
        x_opt = res.x
    prob, fid, loss, _ = simulate_and_metrics(x_opt, target_state, N)
    return x_opt, {"loss": loss, "fidelity": fid, "prob": prob, "nit": res.nit, "success": res.success}


# ---- Parameter sets from Table 1 (paper) ----
# Values for angles given as fractions of π in the paper.
# Table columns: r, xi_db, alpha, phi_bs(π), theta(π), |xi|, phi_xi(π), |beta|, phi_beta(π), Fidelity, Probability
TABLE1 = [
    # Target 1
    dict(r=0.15, xi_db=5.0, alpha=5.0e-4, phi_bs=0.49993, theta=0.50034, xi_abs=3.0e-4, phi_xi=0.50023, beta_abs=1.5725, phi_beta=1.50020, F_exp=0.9933, P_exp=5.43719585e-07),
    # Target 2
    dict(r=0.20, xi_db=5.0, alpha=8.60e-2, phi_bs=0.48977, theta=0.49512, xi_abs=4.01e-2, phi_xi=0.49624, beta_abs=1.6164, phi_beta=1.49846, F_exp=0.9910, P_exp=1.383756e-02),
    # Target 3
    dict(r=0.15, xi_db=6.0, alpha=3.40e-3, phi_bs=0.49958, theta=0.49908, xi_abs=2.00e-3, phi_xi=0.49985, beta_abs=1.6200, phi_beta=1.49991, F_exp=0.9861, P_exp=2.71714657e-05),
]


def params_from_table1(idx: int):
    """Return x vector and associated target specification for Table1 target index (1-based)."""
    entry = TABLE1[idx - 1]
    x = [
        entry['alpha'],
        entry['phi_bs'] * np.pi,
        entry['theta'] * np.pi,
        entry['xi_abs'],
        entry['phi_xi'] * np.pi,
        entry['beta_abs'],
        entry['phi_beta'] * np.pi,
    ]
    return x, entry


if __name__ == "__main__":
    # Truncation dimension (paper uses large enough cutoff; adjust if convergence issues)
    N = 50
    xvec = np.linspace(-5, 5, 200)

    print("Reproducing Table 1 targets (paper) with current implementation:\n")
    for idx in (1, 2, 3):
        x, entry = params_from_table1(idx)
        target = create_target_state(entry['r'], entry['xi_db'], N)
        prob, fid, loss, rho2 = simulate_and_metrics(x, target, N)
        print(f"Target{idx}: r={entry['r']}, xi_db={entry['xi_db']} | Expected F={entry['F_exp']}, P≈{entry['P_exp']}")
        print(f"  Obtained: F={fid:.6f}, P={prob:.6e}, loss={loss:.6e}")
        dF = fid - entry['F_exp']
        dP = prob - entry['P_exp']
        print(f"  Deltas: ΔF={dF:+.2e}, ΔP={dP:+.2e}\n")

        # Diagnostics: try alternative conventions only for first target to limit runtime
        if idx == 1:
            variants = []
            for bs in ['plus', 'minus']:
                for tms in ['std', 'alt']:
                    variants.append((bs, tms))
            a1, a2, I = _ops_two_mode(N)
            psi0 = qt.tensor(qt.fock(N, 2), qt.fock(N, 0))
            for (bs, tms) in variants:
                Uv = unitary_total_variant(x, N, bs_form=bs, tms_sign=tms)
                psi_evolved = Uv * psi0
                Pi = qt.tensor(qt.fock(N, 2) * qt.fock(N, 2).dag(), I)
                rho = psi_evolved * psi_evolved.dag()
                rho_post = Pi * rho * Pi
                p = float(np.real(rho_post.tr()))
                if p > 0:
                    rho2_v = qt.ptrace(rho_post, 1) / p
                    Fv = float(np.real(qt.expect(qt.ket2dm(target), rho2_v)))
                else:
                    Fv = 0.0
                print(f"    Variant bs={bs}, tms={tms}: F={Fv:.6f}, P={p:.3e}")
            print()

        if idx == 1 and rho2 is not None:
            # Plot only first target example Wigner functions
            Wt = qt.wigner(target, xvec, xvec, g=np.sqrt(2))
            Wr = qt.wigner(rho2, xvec, xvec, g=np.sqrt(2))
            plt.figure(figsize=(10, 4))
            plt.subplot(1, 2, 1)
            norm_t = TwoSlopeNorm(vmin=Wt.min(), vcenter=0, vmax=Wt.max())
            plt.contourf(xvec, xvec, Wt, 150, cmap='RdBu_r', norm=norm_t)
            plt.title('Target Wigner (T1)')
            plt.xlabel('q')
            plt.ylabel('p')
            plt.colorbar()
            plt.subplot(1, 2, 2)
            norm_r = TwoSlopeNorm(vmin=Wr.min(), vcenter=0, vmax=Wr.max())
            plt.contourf(xvec, xvec, Wr, 150, cmap='RdBu_r', norm=norm_r)
            plt.title('Output Wigner (T1)')
            plt.xlabel('q')
            plt.ylabel('p')
            plt.colorbar()
            plt.tight_layout()
            plt.show()

    # Extended diagnostics for Target1: scan scaling factors
    print("Diagnostic scan (Target1) over scaling factors...")
    x1, entry1 = params_from_table1(1)
    bs_scales = [1.0, 0.5]
    tms_scales = [1.0, 0.5]
    cubic_scales = [1.0, 3.0, 6.0]
    target_base = create_target_state(entry1['r'], entry1['xi_db'], N)
    a1, a2, I = _ops_two_mode(N)
    psi0 = qt.tensor(qt.fock(N, 2), qt.fock(N, 0))
    Pi = qt.tensor(qt.fock(N, 2) * qt.fock(N, 2).dag(), I)
    for bss in bs_scales:
        for tmss in tms_scales:
            for cscale in cubic_scales:
                target_scaled = create_target_state_scaled(entry1['r'], entry1['xi_db'], N, cubic_scale=cscale)
                U = unitary_scaled(x1, N, bs_scale=bss, tms_scale=tmss, cubic_scale=cscale)
                psi_e = U * psi0
                rho = psi_e * psi_e.dag()
                prob = float(np.real((Pi * rho).tr()))
                if prob > 0:
                    rho2 = qt.ptrace(Pi * rho * Pi, 1) / prob
                    F = float(np.real(qt.expect(qt.ket2dm(target_scaled), rho2)))
                else:
                    F = 0.0
                print(f"  bs_scale={bss}, tms_scale={tmss}, cubic_scale={cscale}: P={prob:.3e}, F={F:.4f}")

    # x_opt, info = optimize_params(target, N, maxiter=30)
    # print("Optimized params:", x_opt)
    # print("Metrics:", info)

